<template>
  <div>充电站监控</div>
  <el-card>
    <el-row :gutter="24">
      <el-col :span="6">
        <el-input v-model="formParams.search">
          <template #append>
            <el-select v-model="select" style="width: 115px">
              <el-option label="按名称查询" value="name"></el-option>
              <el-option label="按ID查询" value="id"></el-option>
            </el-select>
          </template>
        </el-input>

      </el-col>
      <el-col :span="6">
        <el-select v-model="formParams.status">
          <el-option label="全部" :value="1"></el-option>
          <el-option label="使用中" :value="2"></el-option>
          <el-option label="空闲中" :value="3"></el-option>
        </el-select>
      </el-col>
      <el-col :span="6" :offset="6">
        <el-button type="primary">查询</el-button>
        <el-button>重置</el-button>
      </el-col>
    </el-row>
  </el-card>

  <el-card class="mt">
    <el-row>
      <el-col :span="6">
        <el-statistic title="累计充电量(度)" :value="268900" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="累计充电次数(次)" :value="1389"></el-statistic>
      </el-col>
      <el-col :span="6">
        <el-statistic title="服务区域(个)" :value="88" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="累计效益(元)" :value="5622178"></el-statistic>
      </el-col>
    </el-row>
  </el-card>

  <el-card class="mt">
    <el-button type="primary" icon="Plus" @click="visible=true">新增充电站</el-button>
  </el-card>

  <el-card class="mt">
    <el-table :data="tableData">            
      <el-table-column type="index" label="序号" width="80"/>
      <el-table-column prop="name" label="站点名称" />
      <el-table-column prop="id" label="站点ID" />
      <el-table-column prop="city" label="所属城市" />
      <el-table-column prop="fast" label="快充数" />
      <el-table-column prop="slow" label="慢充数" />
      <el-table-column prop="status" label="充电站状态" />
      <el-table-column prop="now" label="正在充电" />
      <el-table-column prop="fault" label="充电故障" />
      <el-table-column prop="person" label="站点负责人" />
      <el-table-column prop="tel" label="负责人电话" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" size="small">编辑</el-button>
          <el-button type="danger" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="fr mt mb"
      v-model:current-page="pageInfo.page"
      v-model:page-size="pageInfo.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />   
  </el-card>
 <station-form :visible="visible" ></station-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {getTableData} from "@/api/chargingstation/tabledata"
import type {RowType} from "@/type/RowType"
import StationForm from "./components/StationForm.vue"
const select = ref("name")
const formParams = ref({
  search: "", //用ID 还是 名称查询
  status: 1
})
const pageInfo=reactive({
  page:1,
  pageSize:10
})
const tableData=ref<RowType[]>([])
const total=ref(0)
const visible=ref(true)
const loadData= async ()=>{        //[select.value]意思是选择的是ID还是名称
  const res = await getTableData({[select.value]:formParams.value.search,status:formParams.value.status,...pageInfo})
  tableData.value=res.data.list
  total.value=res.data.total
  console.log("======",tableData.value)
}
loadData()

function handleSizeChange(page:number){
  pageInfo.page=page
  loadData()
}

function handleCurrentChange(CurrentPage:number){
  pageInfo.page=CurrentPage
  loadData()
}
</script>